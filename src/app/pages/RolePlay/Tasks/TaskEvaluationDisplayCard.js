import React, {useState, useEffect, useRef, forwardRef, useImperativeHandle, useCallback} from 'react';
import {
  Card,
  Table,
  Typography,
  Switch,
  Alert,
  Tooltip,
  Button,
  Input,
  InputNumber,
  Popover,
  Form,
  message,
  Space,
  Modal,
} from 'antd';
import {
  QuestionCircleOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  EditOutlined,
  RobotOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import {useTranslation} from 'react-i18next';
import './TaskEvaluationDisplayCard.scss';
import {
  createRoleplayTask,
  updateRoleplayTask,
  deleteRoleplayTask,
  createTasksFromPrompt,
} from '@src/app/services/RolePlay/TaskService';
import AntButton from '@src/app/component/AntButton';
import {BUTTON, API} from '@constant';
import { toast } from '@component/ToastProvider';

const {Text, Paragraph} = Typography;
const {TextArea} = Input;

// Editable Cell Component (Slightly modified to handle different input types)
const EditableCell = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  handleSave,
  toggleEdit, // Pass toggleEdit to cell
  ...restProps
}) => {
  const inputRef = useRef(null);
  const [form] = Form.useForm();
  const [isEditingThisCell, setIsEditingThisCell] = useState(false);

  useEffect(() => {
    if ((isEditingThisCell || editing) && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditingThisCell, editing]);

  useEffect(() => {
    if (editing && !isEditingThisCell) {
      setIsEditingThisCell(true);
      form.setFieldsValue({[dataIndex]: record[dataIndex]});
    }
  }, [editing, dataIndex, record, form, isEditingThisCell]);

  const startEdit = () => {
    setIsEditingThisCell(true);
    form.setFieldsValue({[dataIndex]: record[dataIndex]});
    if (toggleEdit) toggleEdit(record.key || record._id, dataIndex, true); // Notify parent about edit start
  };

  const save = async () => {
    try {
      const values = await form.validateFields();
      console.log('=== EDITABLE CELL SAVE ===');
      console.log('DataIndex:', dataIndex);
      console.log('Record:', record);
      console.log('Values:', values);
      console.log('HandleSave function:', !!handleSave);

      setIsEditingThisCell(false);
      if (handleSave) {
        const updatedRecord = {...record, ...values};
        console.log('Calling handleSave with:', updatedRecord);
        handleSave(updatedRecord);
      }
      if (toggleEdit) toggleEdit(record.key || record._id, dataIndex, false);
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  let childNode = children;

  if (record) {
    // Ensure record exists
    if (editing || isEditingThisCell) {
      if (inputType === 'textarea') {
        childNode = (
          <Form form={form} component={false}>
            <Form.Item
              style={{margin: 0}}
              name={dataIndex}
              rules={[
                {required: title === 'Topic' || title === 'Evaluation guidelines', message: `${title} is required.`},
              ]}
            >
              <TextArea
                ref={inputRef}
                onPressEnter={e => {
                  e.preventDefault();
                  save();
                }}
                onBlur={save}
                onChange={e => {
                  // Cập nhật dữ liệu ngay lập tức khi người dùng nhập
                  const newValue = e.target.value;
                  const updatedRecord = {...record, [dataIndex]: newValue};
                  console.log(`=== IMMEDIATE UPDATE - ${dataIndex} ===`);
                  console.log('New value:', newValue);
                  console.log('Updated record:', updatedRecord);
                  if (handleSave) {
                    handleSave(updatedRecord);
                  }
                }}
                autoSize={{minRows: 2, maxRows: 6}}
              />
            </Form.Item>
          </Form>
        );
      } else {
        // Default to Input
        childNode = (
          <Form form={form} component={false}>
            <Form.Item
              style={{margin: 0}}
              name={dataIndex}
              rules={[{required: title === 'Topic', message: `${title} is required.`}]}
            >
              <Input
                ref={inputRef}
                onPressEnter={save}
                onBlur={save}
                onChange={e => {
                  // Cập nhật dữ liệu ngay lập tức khi người dùng nhập
                  const newValue = e.target.value;
                  const updatedRecord = {...record, [dataIndex]: newValue};
                  console.log(`=== IMMEDIATE UPDATE - ${dataIndex} ===`);
                  console.log('New value:', newValue);
                  console.log('Updated record:', updatedRecord);
                  if (handleSave) {
                    handleSave(updatedRecord);
                  }
                }}
              />
            </Form.Item>
          </Form>
        );
      }
    } else {
      childNode = (
        <div className="editable-cell-value-wrap" onClick={startEdit} style={{cursor: 'pointer'}}>
          {children || <Text type="secondary" italic>{`Click to add ${title.toLowerCase()}`}</Text>}
        </div>
      );
    }
  }

  return <td {...restProps}>{childNode}</td>;
};

export const TaskEvaluationDisplayCard = forwardRef(({
  dataSource = [],
  onTaskAdd,
  onTaskUpdate,
  onTaskDelete,
  courseId, // Nhận courseId từ parent component
  hideButtons = false, // Prop để ẩn buttons
  ...restProps
}, ref) => {
  const {t} = useTranslation();
  const [editingCellKey, setEditingCellKey] = useState(''); // To track which cell is being edited e.g. "rowKey-dataIndex"
  const [modifiedRows, setModifiedRows] = useState({}); // Track rows that have been modified and need saving
  const [modifiedRowIds, setModifiedRowIds] = useState(new Set()); // Simple Set to track modified row IDs
  const modifiedRowsRef = useRef({}); // Backup ref to prevent state loss
  const [forceRender, setForceRender] = useState(0); // Force re-render when needed
  const [isSavingAll, setIsSavingAll] = useState(false); // Loading state for save all
  const [newTaskId, setNewTaskId] = useState(null); // Keep track of the most recently added task
  const [isAIModalVisible, setIsAIModalVisible] = useState(false); // Trạng thái hiển thị modal tạo từ AI
  const [userPrompt, setUserPrompt] = useState(''); // Prompt người dùng nhập vào
  const [isGeneratingWithAI, setIsGeneratingWithAI] = useState(false); // Trạng thái đang tạo từ AI


  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    showAIModal: () => setIsAIModalVisible(true),
  }));

  // Force re-render when dataSource changes to ensure UI reflects latest data
  const prevDataSourceRef = useRef();
  useEffect(() => {
    // Only trigger if dataSource actually changed (not just reference)
    const currentIds = dataSource.map(t => t._id).sort().join(',');
    const prevIds = prevDataSourceRef.current?.map(t => t._id).sort().join(',') || '';

    if (currentIds !== prevIds) {
      console.log('=== DATASOURCE CHANGED ===');
      console.log('New dataSource:', dataSource.map(t => ({id: t._id, name: t.name})));
      setForceRender(prev => prev + 1);
    }

    prevDataSourceRef.current = dataSource;
  }, [dataSource]);

  // Kiểm tra xem một cell có đang ở chế độ chỉnh sửa không
  const isCellEditing = (record, dataIndex) => {
    return editingCellKey === `${record._id}-${dataIndex}` || record._id === newTaskId;
  };

  const handleToggleEdit = (rowKey, dataIndex, isEditing) => {
    setEditingCellKey(isEditing ? `${rowKey}-${dataIndex}` : '');
  };

  // Helper function to check if a row needs saving
  const needsSaving = (record) => {
    // New rows (temp id) always need saving
    if (record._id.toString().startsWith('temp_')) {
      return true;
    }

    // Check both modifiedRows object and modifiedRowIds Set
    const isModifiedInObject = modifiedRows[record._id];
    const isModifiedInSet = modifiedRowIds.has(record._id);

    return !!isModifiedInObject || isModifiedInSet;
  };

  const handleSave = useCallback(updatedRecord => {
    console.log('=== HANDLE SAVE - IMMEDIATE UPDATE ===');
    console.log('Updated record:', updatedRecord);

    // Xử lý helpfulLinks nếu là chuỗi (người dùng nhập vào)
    if (typeof updatedRecord.helpfulLinks === 'string') {
      updatedRecord.helpfulLinks = updatedRecord.helpfulLinks
        .split(',')
        .map(link => link.trim())
        .filter(Boolean);
    }

    // Mark row as modified (needs saving) và cập nhật ngay lập tức vào modifiedRows
    setModifiedRows(prev => {
      const updated = {
        ...prev,
        [updatedRecord._id]: updatedRecord
      };
      // Keep ref in sync with state
      modifiedRowsRef.current = updated;
      console.log('Updated modifiedRows:', updated);
      return updated;
    });

    setModifiedRowIds(prev => {
      const newSet = new Set(prev);
      newSet.add(updatedRecord._id);
      console.log('Updated modifiedRowIds:', Array.from(newSet));
      return newSet;
    });

    // Clear newTaskId if this is the task that was just added
    if (updatedRecord._id === newTaskId) {
      setNewTaskId(null);
    }

    // Force re-render để đảm bảo UI hiển thị dữ liệu mới ngay lập tức
    setForceRender(prev => prev + 1);
  }, [newTaskId]);

  // Function to save all modified rows at once
  const handleSaveAll = async () => {
    console.log('=== HANDLE SAVE ALL - START ===');

    const modifiedRowsToSave = Object.keys(modifiedRowsRef.current).length > 0
      ? modifiedRowsRef.current
      : modifiedRows;

    const modifiedRowsArray = Object.values(modifiedRowsToSave);
    console.log('Modified rows to save:', modifiedRowsArray.length);
    console.log('Modified rows data:', modifiedRowsArray.map(r => ({ id: r._id, name: r.name })));

    if (modifiedRowsArray.length === 0) {
      toast.info('Không có dữ liệu nào cần lưu');
      return;
    }

    setIsSavingAll(true);

    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    const successfullyProcessedIds = []; // Track successfully processed IDs
    const successfullyUpdatedTasks = []; // Track successfully updated tasks for UI update

    // Disable multiple toasts by tracking them
    const originalToastSuccess = toast.success;
    const originalToastError = toast.error;

    // Temporarily disable individual toasts
    toast.success = () => {};
    toast.error = () => {};

    try {
      // Process all saves sequentially to avoid race conditions
      for (const record of modifiedRowsArray) {
        try {
          if (record._id.toString().startsWith('temp_')) {
            // NEW TASK - Create
            if (!courseId) {
              throw new Error('Course ID is required');
            }

            const currentTaskCount = dataSource.filter(task =>
              !task._id.toString().startsWith('temp_')
            ).length;
            const orderInScenario = currentTaskCount + successCount + 1;

            const dataToSave = {
              ...record,
              courseId,
              orderInScenario,
            };

            // Clean up data
            if (typeof dataToSave.helpfulLinks === 'string') {
              dataToSave.helpfulLinks = dataToSave.helpfulLinks
                .split(',')
                .map(link => link.trim())
                .filter(Boolean);
            } else if (!Array.isArray(dataToSave.helpfulLinks)) {
              dataToSave.helpfulLinks = [];
            }

            delete dataToSave._id;
            delete dataToSave.key;

            const createdTask = await createRoleplayTask(dataToSave);
            if (createdTask) {
              // Cập nhật parent component ngay lập tức
              const taskWithTempId = {...createdTask, original_temp_id: record._id, key: createdTask._id};
              onTaskUpdate(taskWithTempId);

              successCount++;
              successfullyProcessedIds.push(record._id);
              successfullyUpdatedTasks.push(taskWithTempId);
            }
          } else {
            // EXISTING TASK - Update
            const dataToUpdate = {...record};

            // Clean up helpfulLinks
            if (typeof dataToUpdate.helpfulLinks === 'string') {
              dataToUpdate.helpfulLinks = dataToUpdate.helpfulLinks
                .split(',')
                .map(link => link.trim())
                .filter(Boolean);
            } else if (!Array.isArray(dataToUpdate.helpfulLinks)) {
              dataToUpdate.helpfulLinks = [];
            }

            const updatedTask = await updateRoleplayTask(dataToUpdate);
            if (updatedTask) {
              successCount++;
              successfullyProcessedIds.push(record._id);
              successfullyUpdatedTasks.push(updatedTask);
            }
          }
        } catch (error) {
          console.error(`Error saving record ${record._id}:`, error);
          errorCount++;
          errors.push({
            id: record._id,
            name: record.name || 'Unknown task',
            error: error.message
          });
        }
      }

      // Only clear successfully processed records from modified states
      if (successfullyProcessedIds.length > 0) {
        console.log('=== CLEARING MODIFIED STATES FOR SUCCESSFUL RECORDS ===');
        console.log('Successfully processed IDs:', successfullyProcessedIds);

        // Xóa các dòng đã lưu thành công khỏi modifiedRows
        setModifiedRows(prev => {
          const updated = {...prev};
          successfullyProcessedIds.forEach(id => {
            delete updated[id];
          });
          console.log('Remaining modified rows after save all:', Object.keys(updated));
          return updated;
        });

        // Xóa các dòng đã lưu thành công khỏi modifiedRowIds
        setModifiedRowIds(prev => {
          const newSet = new Set(prev);
          successfullyProcessedIds.forEach(id => {
            newSet.delete(id);
          });
          console.log('Remaining modified row IDs after save all:', Array.from(newSet));
          return newSet;
        });

        // Update ref to match current state
        modifiedRowsRef.current = {...modifiedRowsRef.current};
        successfullyProcessedIds.forEach(id => {
          delete modifiedRowsRef.current[id];
        });

        // Clear editing state for successfully processed records
        setEditingCellKey(prev => {
          const isEditingProcessedRecord = successfullyProcessedIds.some(id =>
            prev.startsWith(`${id}-`)
          );
          if (isEditingProcessedRecord) {
            console.log('Clearing editing state for processed record');
            return '';
          }
          return prev;
        });

        // Force re-render để đảm bảo UI hiển thị đúng dữ liệu
        setForceRender(prev => prev + 1);

        // Notify parent component về tất cả tasks đã được update để refresh dataSource
        if (onTaskUpdate && successfullyUpdatedTasks.length > 0) {
          console.log('=== NOTIFYING PARENT OF ALL UPDATED TASKS ===');
          for (const task of successfullyUpdatedTasks) {
            console.log('Notifying parent of updated task:', task._id);
            onTaskUpdate(task);
            // Small delay between notifications to ensure proper state updates
            await new Promise(resolve => setTimeout(resolve, 10));
          }

          // Additional delay to ensure all parent state updates are complete
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

    } finally {
      // Restore toast functions
      toast.success = originalToastSuccess;
      toast.error = originalToastError;

      // Show summary notification
      if (successCount > 0 && errorCount === 0) {
        toast.success(`Đã lưu thành công ${successCount} nhiệm vụ`);
      } else if (successCount > 0 && errorCount > 0) {
        toast.warning(`Đã lưu ${successCount} nhiệm vụ, ${errorCount} nhiệm vụ lỗi`);
      } else if (errorCount > 0) {
        toast.error(`Lỗi khi lưu ${errorCount} nhiệm vụ`);
      }

      setIsSavingAll(false);
    }
  };

  const handleRowSave = async record => {
    try {
      console.log('=== HANDLE ROW SAVE - SINGLE RECORD ===');
      console.log('Record to save:', record);
      console.log('Current editing cell key:', editingCellKey);
      console.log('Current modified rows:', Object.keys(modifiedRows));

      if (record._id.toString().startsWith('temp_')) {
        // NEW TASK - Create
        console.log('Creating new task...');

        if (!courseId) {
          toast.error(t('COURSE_ID_REQUIRED', 'Course ID is required'));
          return;
        }

        // Tính toán orderInScenario dựa vào số lượng task hiện có
        const currentTaskCount = dataSource.filter(task =>
          !task._id.toString().startsWith('temp_') // Chỉ đếm tasks đã được lưu
        ).length;
        const orderInScenario = currentTaskCount + 1;

        const dataToSave = {
          ...record,
          courseId,
          orderInScenario,
        };

        // Đảm bảo helpfulLinks luôn là mảng
        if (typeof dataToSave.helpfulLinks === 'string') {
          dataToSave.helpfulLinks = dataToSave.helpfulLinks
            .split(',')
            .map(link => link.trim())
            .filter(Boolean);
        } else if (!Array.isArray(dataToSave.helpfulLinks)) {
          dataToSave.helpfulLinks = [];
        }

        delete dataToSave._id; // Remove temp id
        delete dataToSave.key; // Remove key

        try {
          const createdTask = await createRoleplayTask(dataToSave);
          if (createdTask) {
            // Update parent component with real task data
            onTaskUpdate({...createdTask, original_temp_id: record._id, key: createdTask._id});

            // Small delay to ensure parent state is updated before clearing local state
            await new Promise(resolve => setTimeout(resolve, 50));

            // Remove ONLY this record from modifiedRows and modifiedRowIds
            console.log('=== REMOVING SAVED RECORD FROM MODIFIED STATES ===');
            console.log('Record ID to remove:', record._id);

            setModifiedRows(prev => {
              const updated = {...prev};
              delete updated[record._id];
              console.log('Remaining modified rows after single save:', Object.keys(updated));
              return updated;
            });

            setModifiedRowIds(prev => {
              const newSet = new Set(prev);
              newSet.delete(record._id);
              console.log('Remaining modified row IDs after single save:', Array.from(newSet));
              return newSet;
            });

            // Update ref to match current state
            if (modifiedRowsRef.current[record._id]) {
              delete modifiedRowsRef.current[record._id];
            }

            // Clear editing state ONLY for this record, keep others intact
            setEditingCellKey(prev => {
              if (prev.startsWith(`${record._id}-`)) {
                console.log('Clearing editing state for saved record:', record._id);
                return '';
              }
              console.log('Keeping editing state for other records:', prev);
              return prev;
            });

            // Force re-render để đảm bảo UI hiển thị đúng dữ liệu
            setForceRender(prev => prev + 1);

            toast.success(t('TASK_CREATED', 'Task created successfully'));
          }
        } catch (error) {
          console.error('Error creating task:', error);
          toast.error(t('CREATE_ERROR', 'Failed to create task'));
        }
      } else {
        // EXISTING TASK - Update
        console.log('Updating existing task...');

        const dataToUpdate = {...record};

        // Đảm bảo helpfulLinks luôn là mảng
        if (typeof dataToUpdate.helpfulLinks === 'string') {
          dataToUpdate.helpfulLinks = dataToUpdate.helpfulLinks
            .split(',')
            .map(link => link.trim())
            .filter(Boolean);
        } else if (!Array.isArray(dataToUpdate.helpfulLinks)) {
          dataToUpdate.helpfulLinks = [];
        }

        try {
          const updatedTask = await updateRoleplayTask(dataToUpdate);
          if (updatedTask) {
            // Remove ONLY this record from modifiedRows and modifiedRowIds since it's been saved
            console.log('=== REMOVING SAVED RECORD FROM MODIFIED STATES (UPDATE) ===');
            console.log('Record ID to remove:', record._id);

            setModifiedRows(prev => {
              const updated = {...prev};
              delete updated[record._id];
              console.log('Remaining modified rows after single update:', Object.keys(updated));
              return updated;
            });

            setModifiedRowIds(prev => {
              const newSet = new Set(prev);
              newSet.delete(record._id);
              console.log('Remaining modified row IDs after single update:', Array.from(newSet));
              return newSet;
            });

            // Update ref to match current state
            if (modifiedRowsRef.current[record._id]) {
              delete modifiedRowsRef.current[record._id];
            }

            // Clear editing state ONLY for this record, keep others intact
            setEditingCellKey(prev => {
              if (prev.startsWith(`${record._id}-`)) {
                return '';
              }
              return prev;
            });

            // Notify parent component first
            if (onTaskUpdate) {
              onTaskUpdate(updatedTask);
            }

            // Small delay to ensure parent state is updated before clearing local state
            await new Promise(resolve => setTimeout(resolve, 50));

            // Force re-render để đảm bảo UI hiển thị đúng dữ liệu
            setForceRender(prev => prev + 1);

            toast.success(t('TASK_UPDATED', 'Task updated successfully'));
          } else {
            toast.error(t('UPDATE_ERROR', 'Failed to update task'));
          }
        } catch (error) {
          console.error('Error updating task:', error);
          toast.error(t('UPDATE_ERROR', 'Failed to update task'));
        }
      }
    } catch (error) {
      console.error('Error saving task:', error);
      toast.error(t('SAVE_ERROR', 'Failed to save task'));
    }
  };

  const handleAddTask = () => {
    const newId = `temp_${Date.now()}`;
    const newTask = {
      _id: newId,
      name: '',
      description: '',
      evaluationGuidelines: '',
      weight: 0,
      exampleVideoUrl: '',
      helpfulLinks: [],
      isMakeOrBreak: false,
    };

    onTaskAdd(newTask);

    // Add to newRows
    setNewRows(prev => ({
      ...prev,
      [newTask._id]: newTask,
    }));

    // Set this as the new task being edited
    setNewTaskId(newId);
  };

  // Hiển thị modal nhập prompt AI
  const showAIModal = () => {
    setIsAIModalVisible(true);
    setUserPrompt('');
  };

  // Đóng modal nhập prompt AI
  const handleAIModalCancel = () => {
    setIsAIModalVisible(false);
    setUserPrompt('');
  };

  // Xử lý tạo task từ AI
  const handleGenerateWithAI = async () => {
    if (!courseId) {
      toast.error(t('COURSE_ID_REQUIRED', 'Course ID is required'));
      return;
    }

    // scenarioId không cần thiết vì model đã xóa field này

    if (!userPrompt.trim()) {
      toast.error(t('PROMPT_REQUIRED', 'Please enter a prompt'));
      return;
    }

    try {
      setIsGeneratingWithAI(true);
      const result = await createTasksFromPrompt(courseId, userPrompt.trim());

      // Xử lý nhiều định dạng response có thể từ AI
      let tasksArray = [];

      if (result && result.tasks && Array.isArray(result.tasks)) {
        tasksArray = result.tasks;
      } else if (result && Array.isArray(result)) {
        tasksArray = result;
      } else if (result && result.data && Array.isArray(result.data)) {
        tasksArray = result.data;
      } else if (result && result.data && result.data.tasks && Array.isArray(result.data.tasks)) {
        tasksArray = result.data.tasks;
      } else if (result && typeof result === 'object' && result.name) {
        tasksArray = [result];
      } else {
        // Thử tìm tasks trong các nested properties
        const findTasks = (obj, path = '') => {
          if (Array.isArray(obj)) {
            return obj;
          }
          if (obj && typeof obj === 'object') {
            for (const [key, value] of Object.entries(obj)) {
              if (key.toLowerCase().includes('task') && Array.isArray(value)) {
                return value;
              }
              const nested = findTasks(value, `${path}.${key}`);
              if (nested) return nested;
            }
          }
          return null;
        };

        const foundTasks = findTasks(result);
        if (foundTasks) {
          tasksArray = foundTasks;
        }
      }

      if (tasksArray.length > 0) {
        // Tính toán orderInScenario cho các task từ AI
        const currentTaskCount = dataSource.filter(task =>
          !task._id.toString().startsWith('temp_') // Chỉ đếm tasks đã được lưu
        ).length;

        // Thêm các task mới vào danh sách
        const newTasks = tasksArray.map((task, index) => {
          const newId = `temp_${Date.now()}_${index}`;
          const processedTask = {
            name: task.name || task.topic || task.title || `Task ${index + 1}`,
            description: task.description || task.desc || task.content || '',
            evaluationGuidelines: task.evaluationGuidelines || task.guidelines || task.criteria || task.evaluation || '',
            weight: parseInt(task.weight) || parseInt(task.score) || parseInt(task.points) || 0,
            exampleVideoUrl: task.exampleVideoUrl || task.videoUrl || task.video || '',
            helpfulLinks: task.helpfulLinks || task.links || task.resources || [],
            isMakeOrBreak: Boolean(task.isMakeOrBreak || task.critical || task.required),
            _id: newId,
            key: newId,
            orderInScenario: currentTaskCount + index + 1, // Thêm thứ tự cho task từ AI
          };

          return processedTask;
        });


        // Thêm các task mới vào state
        const newRowsToAdd = {};
        newTasks.forEach((task, index) => {
          newRowsToAdd[task._id] = task;
        });


        setNewRows(prev => {
          const updated = {
            ...prev,
            ...newRowsToAdd,
          };
          return updated;
        });

        // Thay vì gọi onTaskAdd từng task riêng biệt, gọi một lần với tất cả tasks
        if (onTaskAdd && typeof onTaskAdd === 'function') {
          // Gọi onTaskAdd với một object chứa tất cả tasks
          onTaskAdd({
            type: 'BULK_ADD',
            tasks: newTasks
          });
        } else {
          // Fallback: gọi từng task riêng biệt nếu parent không hỗ trợ bulk add
          newTasks.forEach((task, index) => {
            if (onTaskAdd) onTaskAdd(task);
          });
        }

        toast.success(t('TASKS_GENERATED', `Đã tạo thành công ${newTasks.length} nhiệm vụ từ AI`));
        setIsAIModalVisible(false);
        setUserPrompt(''); // Reset prompt
      } else {
        console.warn('No valid tasks found in AI response:', result);
        toast.error(t('GENERATE_ERROR', 'AI không trả về nhiệm vụ hợp lệ. Vui lòng thử lại với prompt khác.'));
      }
    } catch (error) {
      console.error('Error generating tasks:', error);
      toast.error(t('GENERATE_ERROR', 'Không thể tạo nhiệm vụ từ AI. Vui lòng thử lại.'));
    } finally {
      setIsGeneratingWithAI(false);
    }
  };

  const handleDeleteTask = async taskId => {
    try {
      // Nếu là task tạm thời (chưa lưu vào DB)
      if (taskId.toString().startsWith('temp_')) {
        onTaskDelete(taskId);
        return;
      }

      // Hiển thị modal xác nhận xóa
      Modal.confirm({
        title: t('CONFIRM_DELETE_TASK', 'Are you sure you want to delete this task?'),
        content: t('DELETE_TASK_WARNING', 'This action cannot be undone.'),
        okText: t('DELETE', 'Delete'),
        okType: 'danger',
        cancelText: t('CANCEL', 'Cancel'),
        onOk: async () => {
          try {
            await deleteRoleplayTask(taskId);
            toast.success(t('TASK_DELETED', 'Task deleted successfully'));
            onTaskDelete(taskId);
          } catch (error) {
            console.error('Error deleting task:', error);
            toast.error(t('DELETE_ERROR', 'Failed to delete task'));
          }
        },
      });
    } catch (error) {
      console.error('Error in handleDeleteTask:', error);
      toast.error(t('DELETE_ERROR', 'Failed to delete task'));
    }
  };

  const columns = [
    {
      title: (
        <Tooltip title={t('TASK_TOPIC_TOOLTIP', 'The main subject.')}>
          <Text strong>{t('TOPIC_COLUMN', 'Topic')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'name',
      key: 'name',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t('TOPIC_COLUMN', 'Topic')}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('EVALUATION_GUIDELINES_TOOLTIP', 'Criteria for evaluation.')}>
          <Text strong>{t('EVALUATION_GUIDELINES_COLUMN', 'Evaluation guidelines')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'evaluationGuidelines',
      key: 'evaluationGuidelines',
      width: '24%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t(
            'EVALUATION_GUIDELINES_COLUMN',
            'Evaluation guidelines',
          )}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('WEIGHT_TOOLTIP', 'Importance percentage.')}>
          <Text strong>{t('WEIGHT_COLUMN', 'Weight')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'weight',
      key: 'weight',
      width: '8%',
      align: 'center',
      render: (text, record) => (
        <InputNumber
          min={0}
          max={100}
          value={text} // Use value instead of defaultValue for controlled component behavior
          formatter={value => `${value}%`}
          parser={value => String(value).replace('%', '')}
          onChange={value => {
            const updatedRecord = {...record, weight: value};
            handleSave(updatedRecord);
          }}
          style={{width: '100%', maxWidth: '80px'}}
        />
      ),
    },
    {
      title: (
        <Tooltip title={t('EXAMPLE_VIDEOS_TOOLTIP', 'Link to example video.')}>
          <Text strong>{t('EXAMPLE_VIDEOS_COLUMN', 'Example videos')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'exampleVideoUrl',
      key: 'exampleVideoUrl',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t('HELPFUL_LINKS_COLUMN', 'Helpful links')}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('HELPFUL_LINKS_TOOLTIP', 'Link to helpful resources. Separate multiple links with commas.')}>
          <Text strong>{t('HELPFUL_LINKS_COLUMN', 'Helpful links')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'helpfulLinks',
      key: 'helpfulLinks',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: links => {
        if (!links || (Array.isArray(links) && links.length === 0)) {
          return (
            <Text type="secondary" italic>
              Enter links separated by commas
            </Text>
          );
        }
        return Array.isArray(links) ? links.join(', ') : links;
      },
    },
    {
      title: (
        <Tooltip title={t('MAKE_OR_BREAK_TOOLTIP', 'Critical for passing.')}>
          <Text strong>{t('MAKE_OR_BREAK_COLUMN', 'Make or Break')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'isMakeOrBreak',
      key: 'isMakeOrBreak',
      width: '8%',
      align: 'center',
      render: (isMakeOrBreak, record) => (
        <Switch
          checked={isMakeOrBreak}
          onChange={checked => {
            const updatedRecord = {...record, isMakeOrBreak: checked};
            handleSave(updatedRecord);
          }}
        />
      ),
    },
    {
      title: t('ACTIONS_COLUMN', 'Actions'),
      key: 'actions',
      width: '8%',
      align: 'center',
      render: (_, record) => {
        const showSaveButton = needsSaving(record);

        return (
          <Space>
            {showSaveButton && (
              <Tooltip title={t('SAVE_TASK_TOOLTIP', 'Save this task/topic')}>
                <Button
                  type="primary"
                  size="small"
                  icon={<SaveOutlined />}
                  onClick={() => handleRowSave(record)}
                />
              </Tooltip>
            )}
            <Tooltip title={t('DELETE_TASK_TOOLTIP', 'Delete this task/topic')}>
              <Button
                type="text"
                danger
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteTask(record._id)}
              />
            </Tooltip>
          </Space>
        );
      },
    },
  ];

  const mergedColumns = columns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: record => ({
        record,
        inputType: col.inputType || 'input',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isCellEditing(record, col.dataIndex),
        handleSave: handleSave,
        toggleEdit: handleToggleEdit,
      }),
    };
  });

  // Check if there are any unsaved changes
  const hasUnsavedChanges = modifiedRowIds.size > 0;
  const unsavedCount = modifiedRowIds.size;

  return (
    <div className="task-evaluation-display-card" style={restProps.style}>
      {!hideButtons && (
        <div style={{
          padding: '16px',
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: '#fafafa'
        }}>
          <Space style={{justifyContent: 'space-between', width: '100%'}}>
            <AntButton onClick={handleAddTask} size={'large'} icon={<PlusOutlined />} type={BUTTON.DEEP_NAVY}>
              {t('ADD_TASK_TOPIC', 'Add Topic / Task')}
            </AntButton>
            <AntButton
              onClick={showAIModal}
              icon={<RobotOutlined />}
              disabled={!courseId}
              type={'primary'}
              style={{background: '#722ED1'}}
              size={'large'}
              data-testid="ai-create-button"
            >
              {t('CREATE_FROM_AI', 'Create from AI')}
            </AntButton>
          </Space>
        </div>
      )}

      {/* Modal nhập prompt AI */}
      <Modal
        title={t('CREATE_TASKS_FROM_AI', 'Create Tasks from AI')}
        open={isAIModalVisible}
        onCancel={handleAIModalCancel}
        footer={[
          <AntButton key="cancel" onClick={handleAIModalCancel} size={'large'}>
            {t('CANCEL', 'Cancel')}
          </AntButton>,
          <AntButton
            key="generate"
            type="primary"
            loading={isGeneratingWithAI}
            onClick={handleGenerateWithAI}
            style={{background: '#722ED1'}}
            size={'large'}
          >
            {t('GENERATE', 'Generate')}
          </AntButton>,
        ]}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('PROMPT_LABEL', 'Describe the tasks you want to generate')}
            help={t('PROMPT_HELP', 'For example: "conversation between a student and a potential customer"')}
          >
            <TextArea
              rows={4}
              value={userPrompt}
              onChange={e => setUserPrompt(e.target.value)}
              placeholder={t('PROMPT_PLACEHOLDER', 'Enter your prompt here...')}
            />
          </Form.Item>
        </Form>
      </Modal>

      <div className="table-container">
        {(() => {
          // Merge dataSource with modifiedRows to show updated values immediately
          console.log('=== RENDERING TABLE DATA ===');
          console.log('DataSource tasks:', dataSource.map(t => ({id: t._id, name: t.name})));
          console.log('Modified rows:', Object.keys(modifiedRows));

          const tableDataSource = dataSource.map(task => {
            const modifiedTask = modifiedRows[task._id];
            if (modifiedTask) {
              // Hiển thị dữ liệu đã chỉnh sửa ngay lập tức
              console.log(`Merging modified data for task ${task._id}:`, {
                original: task.name,
                modified: modifiedTask.name
              });
              return{...task, ...modifiedTask, key: task._id};
            }
            return {...task, key: task._id};
          });

          console.log('Final table data:', tableDataSource.map(t => ({id: t._id, name: t.name})));

          return (
            <Table
              key={`table-${forceRender}-${Object.keys(modifiedRows).join('-')}`} // Force re-render when needed
              components={{
                body: {
                  cell: EditableCell,
                },
              }}
              rowClassName="editable-row"
              columns={mergedColumns}
              dataSource={tableDataSource}
              pagination={false}
              className="evaluation-table"
              bordered
            />
          );
        })()}
      </div>

      {/* Warning for unsaved changes - positioned below the table */}
      {hasUnsavedChanges && (
        <div style={{
          padding: '16px',
          borderTop: '1px solid #f0f0f0',
          backgroundColor: '#fafafa'
        }}>
          <Alert
            message={
              <span style={{ fontWeight: '600', color: '#d46b08' }}>
                Bạn có {unsavedCount} nhiệm vụ chưa được lưu
              </span>
            }
            description={
              <span style={{ color: '#8c8c8c' }}>
                Hãy nhấn nút <strong>Save</strong> ở cột Actions để lưu từng nhiệm vụ, hoặc sử dụng nút bên dưới để lưu tất cả.
              </span>
            }
            type="warning"
            showIcon
            style={{
              borderRadius: '8px',
              border: '1px solid #ffd591',
              backgroundColor: '#fff7e6'
            }}
            action={
              <Button
                type="primary"
                size="medium"
                onClick={handleSaveAll}
                loading={isSavingAll}
                disabled={isSavingAll}
                style={{
                  borderRadius: '6px',
                  fontWeight: '500',
                  backgroundColor: '#fa8c16',
                  borderColor: '#fa8c16'
                }}
                icon={!isSavingAll && <SaveOutlined />}
              >
                {isSavingAll ? 'Đang lưu...' : `Lưu tất cả (${unsavedCount})`}
              </Button>
            }
          />
        </div>
      )}
    </div>
  );
});

TaskEvaluationDisplayCard.displayName = 'TaskEvaluationDisplayCard';
