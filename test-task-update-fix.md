# Test Plan: Task Management Table Update Fix

## Mô tả lỗi đã sửa
- **Vấn đề**: <PERSON><PERSON> khi lưu task (đơn lẻ hoặc tất cả), giao diện hiển thị lại dữ liệu cũ thay vì dữ liệu đã cập nhật
- **Nguyên nhân**: State management không đồng bộ giữa các component layers

## Các thay đổi đã thực hiện

### 1. AIScenarioTabs.js
- **C<PERSON><PERSON> thiện onTaskUpdate callback**: Cập nhật scenarios state khi task được update
- **X<PERSON> lý cả task mới và task cũ**: Hỗ trợ cả original_temp_id và _id
- **Force re-render**: Trigger taskUpdateTrigger sau khi cập nhật
- **Đơn giản hóa key**: Loại bỏ JSON.stringify phức tạp trong key

### 2. TaskEvaluationDisplayCard.js  
- **Th<PERSON><PERSON> useEffect theo dõi dataSource**: Force re-render khi dataSource thay đổi
- **<PERSON><PERSON><PERSON> thiện logging**: Thêm console.log để debug dễ hơn
- **Force re-render sau save**: Đảm bảo UI cập nhật ngay sau khi lưu thành công

## Test Cases

### Test Case 1: Lưu từng dòng dữ liệu
1. Mở trang Course Detail với course đã có tasks
2. Chỉnh sửa một task (tên, mô tả, trọng số, etc.)
3. Bấm nút "Lưu" cho dòng đó
4. **Kết quả mong đợi**: 
   - Dữ liệu được lưu thành công (check API response)
   - Giao diện hiển thị ngay dữ liệu mới (không cần reload)
   - Nút "Lưu" biến mất (không còn hiển thị)

### Test Case 2: Lưu tất cả dữ liệu
1. Chỉnh sửa nhiều tasks khác nhau
2. Bấm nút "Lưu tất cả"
3. **Kết quả mong đợi**:
   - Tất cả tasks được lưu thành công
   - Giao diện hiển thị ngay dữ liệu mới cho tất cả tasks
   - Tất cả nút "Lưu" biến mất

### Test Case 3: Tạo task mới
1. Bấm "Thêm task mới"
2. Nhập thông tin và bấm "Lưu"
3. **Kết quả mong đợi**:
   - Task được tạo với ID thật từ server
   - Giao diện hiển thị task với dữ liệu đã lưu
   - Temp ID được thay thế bằng real ID

### Test Case 4: Chuyển đổi giữa các tabs
1. Có nhiều scenarios với tasks khác nhau
2. Chỉnh sửa task ở tab A, lưu thành công
3. Chuyển sang tab B, sau đó quay lại tab A
4. **Kết quả mong đợi**: Dữ liệu ở tab A vẫn hiển thị đúng dữ liệu đã lưu

## Cải tiến bổ sung (v2)

### Vấn đề đã phát hiện từ test:
1. **Save All**: Dữ liệu nhấp nháy (edit → cũ → mới) thiếu chuyên nghiệp
2. **Save Single với multiple rows**: Các rows khác đang edit bị reset

### Giải pháp v2:
1. **Cải thiện handleSaveAll**:
   - Notify parent TRƯỚC khi clear local state
   - Thêm delay để đảm bảo parent state update hoàn tất
   - Tránh hiệu ứng nhấp nháy

2. **Cải thiện handleRowSave**:
   - Notify parent trước khi clear local state
   - Chỉ clear state của row được lưu, bảo toàn rows khác
   - Thêm logging để track preservation

3. **Thêm savingRowIds state**:
   - Track rows đang được save để tránh conflict
   - Ưu tiên hiển thị modified data trong quá trình save
   - Clear state khi save hoàn tất hoặc có lỗi

4. **Cải thiện useEffect dataSource**:
   - Check content thay đổi thay vì chỉ reference
   - Thêm delay để tránh trigger trong quá trình save
   - Tránh infinite loop

5. **Enhanced UX**:
   - Không có hiệu ứng nhấp nháy khi save
   - Bảo toàn trạng thái edit của các rows khác
   - Smooth transition từ modified → saved state

## Test Cases v2

### Test Case 1: Save All với multiple modified rows
1. Edit 3-4 tasks khác nhau
2. Bấm "Lưu tất cả"
3. **Kết quả mong đợi**:
   - Console log hiển thị notification cho tất cả tasks
   - UI cập nhật hiển thị dữ liệu mới cho tất cả
   - Không còn nút "Lưu" nào

### Test Case 2: Save Single khi có multiple modified rows
1. Edit 3 tasks: A, B, C
2. Lưu task A
3. **Kết quả mong đợi**:
   - Task A: hiển thị dữ liệu mới, nút "Lưu" biến mất, KHÔNG có nhấp nháy
   - Task B, C: vẫn hiển thị dữ liệu đã edit, nút "Lưu" vẫn có
   - Console log hiển thị "Other modified rows to preserve: [B_id, C_id]"

### Test Case 3: Mixed operations
1. Edit task A, B
2. Lưu task A
3. Edit task C
4. Lưu tất cả (B, C)
5. **Kết quả mong đợi**: Tất cả hiển thị dữ liệu đúng

## Debug Information
- Console logs được thêm vào để theo dõi:
  - Task update flow với timing
  - State changes trong scenarios
  - DataSource changes với comparison
  - Modified rows state với detailed tracking
  - Parent notification sequence

## Rollback Plan
Nếu có vấn đề, có thể revert các thay đổi bằng cách:
1. Loại bỏ delay logic trong save functions
2. Khôi phục useEffect dataSource đơn giản
3. Loại bỏ sequential notification
4. Loại bỏ các console.log debugging
